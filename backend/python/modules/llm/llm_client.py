"""
LLM客户端模块
提供统一的大模型调用接口
"""
import httpx
import json
from typing import Union, Dict, List, AsyncGenerator

from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

# 敏感词汇模式定义
SENSITIVE_PATTERNS = [
    # 个人信息相关
    {
        "pattern": r"\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b",
        "replacement": "[EMAIL]",
        "description": "邮箱地址"
    },
    {
        "pattern": r"\b(?:password|pwd|passwd|密码)\s*[:=]\s*\S+",
        "replacement": "password: [pwd]",
        "description": "密码信息"
    },
    {
        "pattern": r"\b(?:token|key|secret|秘钥|令牌)\s*[:=]\s*\S+",
        "replacement": "[TOKEN_KEY]",
        "description": "令牌密钥"
    },
    # 中国手机号码
    {
        "pattern": r"\b(?:1[3-9]\d{9})\b",
        "replacement": "[PHONE_NUMBER]",
        "description": "手机号码"
    },
    # 中国身份证号码
    {
        "pattern": r"\b(?:\d{15}|\d{17}[\dXx])\b",
        "replacement": "[IDCARD]",
        "description": "身份证号码"
    },
    # 银行卡号
    {
        "pattern": r"\b(?:\d{16,19})\b",
        "replacement": "[BBBBAND_CARD]",
        "description": "银行卡号"
    },
    # IP地址
    {
        "pattern": r"\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b",
        "replacement": "[IP_ADDR]",
        "description": "IP地址"
    },
    # 数据库连接字符串
    {
        "pattern": r"(?:mysql|postgresql|mongodb|redis)://[^\s]+",
        "replacement": "[DB_URI]",
        "description": "数据库连接"
    }
]

class LLMClient:
    """LLM调用客户端"""

    def __init__(self):
        """
        初始化LLM客户端
        """
        self.config = get_config().llm
        # 创建信号量来限制并发请求数
        self._max_concurrent = getattr(self.config, 'max_concurrent_requests', 10)
        self._semaphore = None  # 延迟初始化，避免在同步上下文中创建

    def _replace_sensitive_words(self, text: str) -> str:
        """
        替换文本中的敏感词汇

        Args:
            text: 需要处理的文本

        Returns:
            str: 替换敏感词后的文本
        """
        if not text:
            return text

        import re

        processed_text = text
        replacements_made = []

        # 遍历所有敏感词模式进行替换
        for pattern_info in SENSITIVE_PATTERNS:
            pattern = pattern_info["pattern"]
            replacement = pattern_info["replacement"]
            description = pattern_info["description"]

            try:
                # 使用正则表达式进行替换
                matches = re.findall(pattern, processed_text, re.IGNORECASE)
                if matches:
                    processed_text = re.sub(pattern, replacement, processed_text, flags=re.IGNORECASE)
                    replacements_made.append({
                        "type": description,
                        "count": len(matches),
                        "original_samples": matches[:3]  # 只记录前3个样本用于调试
                    })
            except re.error as e:
                logger.warning(f"正则表达式错误 - 模式: {pattern}, 错误: {e}")
                continue

        # 记录替换信息（仅在调试模式下）
        if replacements_made:
            logger.debug(f"敏感词替换完成，共处理 {len(replacements_made)} 种类型的敏感信息")
            for replacement in replacements_made:
                logger.debug(f"- {replacement['type']}: {replacement['count']} 个")

        return processed_text

    def _get_headers(self) -> Dict:
        return {
            "Authorization": f"{self.config.api_key}",
            "Content-Type": "application/json"
        }
    
    def _get_request(self, messages: List[Dict], stream: bool = False) -> Dict:
        return {
            "model": self.config.model,
            "messages": messages,
            "stream": stream,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }
    
    async def completion(self, messages: List[Dict[str, str]], stream: bool = False) -> Union[str, AsyncGenerator[str, None]]:
        request_data = self._get_request(messages, stream)
        headers = self._get_headers()

        if stream:
            return self._stream_completion(request_data, headers)
        else:
            return await self._non_stream_completion(request_data, headers)

    async def _stream_completion(self, request_data: dict, headers: dict) -> AsyncGenerator[str, None]:
        """处理流式响应"""
        async with httpx.AsyncClient() as client:
            async with client.stream("POST", self.config.base_url, json=request_data, headers=headers) as response:
                # 检查响应状态
                if response.status_code != 200:
                    logger.error(f"LLM流式调用失败，状态码: {response.status_code}")
                    return

                # 逐行读取流式响应
                async for line in response.aiter_lines():
                    if not line.strip():
                        continue

                    # 处理SSE格式的数据
                    if line.startswith("data: "):
                        data_content = line[6:]  # 移除 "data: " 前缀

                        # 检查是否为结束标志
                        if data_content.strip() == "[DONE]":
                            break

                        try:
                            # 解析JSON数据
                            chunk_data = json.loads(data_content)

                            # 提取内容
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                choice = chunk_data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    chunk_content = choice['delta']['content']
                                    if chunk_content:
                                        yield chunk_content

                        except json.JSONDecodeError as e:
                            logger.warning(f"解析流式响应JSON失败: {e}, 数据: {data_content}")
                            continue
                        except Exception as e:
                            logger.warning(f"处理流式响应块失败: {e}")
                            continue

    async def _non_stream_completion(self, request_data: dict, headers: dict) -> str:
        """处理非流式响应"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.config.base_url,
                    json=request_data,
                    headers=headers,
                    timeout=self.config.timeout
                )

                # logger.info(f"LLM调用响应状态码: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"LLM调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    return ""

                result = response.json()

                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    return content
                else:
                    logger.warning(f"响应中没有choices字段: {result}")
                    return ""

        except httpx.TimeoutException:
            logger.error("LLM调用超时")
            return ""
        except json.JSONDecodeError as e:
            logger.error(f"解析响应JSON失败: {e}")
            return ""
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            return ""

    async def call_async(self, prompt: str, system_prompt: str = "你是一个专业的代码分析助手", stream: bool = False) -> str:
        """
        调用LLM生成回复（异步方法）

        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            stream: 是否使用流式响应

        Returns:
            str: LLM生成的回复
        """ 
        
        # 对输入进行敏感词替换
        safe_prompt = self._replace_sensitive_words(prompt)
        safe_system_prompt = self._replace_sensitive_words(system_prompt)

        # logger.debug(f"LLM调用提示词: {safe_prompt}")

        # 延迟初始化信号量
        if self._semaphore is None:
            import asyncio
            self._semaphore = asyncio.Semaphore(self._max_concurrent)

        # 使用信号量限制并发请求数
        async with self._semaphore:
            try:
                response = await self.completion(
                    messages=[
                        {"role": "system", "content": safe_system_prompt},
                        {"role": "user", "content": safe_prompt}
                    ],
                    stream=stream
                )

                if stream:
                    # 如果是流式响应，需要收集所有块
                    response_text = ""
                    async for chunk in response:
                        response_text += chunk
                    return response_text
                else:
                    # 非流式响应直接返回字符串
                    logger.debug(f"LLM返回: {response}")
                    return response

            except Exception as e:
                logger.error(f"LLM异步调用失败: {e}")
                return ""

# 创建默认的LLM客户端实例
default_llm_client = LLMClient()
