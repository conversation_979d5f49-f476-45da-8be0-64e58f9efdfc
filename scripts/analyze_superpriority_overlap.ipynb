import re
import json
from collections import defaultdict

logs_path = {
    "grep": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-01-20-38-21.log",
    "term_sparse": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-34-59.log",
    "inverted_index": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-39-45.log",
    "embedding": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-08-31-14-04-41.log"
}

# 存储所有工具的结果
all_results = {}  # key为trace_id，value为{"question": question, "recall@5": recall@5, "tool": tool}
question_results = defaultdict(list)  # key为question，value为[{"tool": tool, "recall@5": recall@5}]

for tool_name, log_path in logs_path.items():
    print(f"Processing {tool_name}: {log_path}")
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 找到所有的trace_id
        trace_pattern = r'Trace: ([a-f0-9]{32})'
        trace_ids = re.findall(trace_pattern, content)
        print(f"Found {len(set(trace_ids))} unique trace IDs in {tool_name}")
        
        # 2. 找到所有trace_id对应的question
        question_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, question: ([^\n]+)'
        question_matches = re.findall(question_pattern, content)
        trace_questions = {trace_id: question.strip() for trace_id, question in question_matches}
        print(f"Found {len(trace_questions)} trace-question pairs in {tool_name}")
        
        # 3. 找到所有trace_id对应的metrics
        # 匹配metrics块，包括多行JSON
        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, metrics: (\{[^}]*(?:\n[^}]*)*\})'
        metrics_matches = re.findall(metrics_pattern, content, re.MULTILINE | re.DOTALL)
        
        # 4. 解析metrics并提取Recall@5
        for trace_id, metrics_str in metrics_matches:
            try:
                # 清理metrics字符串，移除可能的换行和额外空格
                cleaned_metrics = re.sub(r'\n\s*', ' ', metrics_str.strip())
                metrics_json = json.loads(cleaned_metrics)
                
                recall_5 = metrics_json.get('Recall@5', None)
                if recall_5 is not None and trace_id in trace_questions:
                    # 5. 记录trace_id对应的信息
                    all_results[trace_id] = {
                        "question": trace_questions[trace_id],
                        "recall@5": recall_5,
                        "tool": tool_name
                    }
                    
                    # 6. 按question分组
                    question_results[trace_questions[trace_id]].append({
                        "tool": tool_name,
                        "recall@5": recall_5
                    })
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse metrics for trace {trace_id}: {e}")
                print(f"Metrics string: {metrics_str[:100]}...")
                continue
        
        print(f"Successfully processed {len([t for t in all_results.values() if t['tool'] == tool_name])} traces for {tool_name}")
        
    except FileNotFoundError:
        print(f"File not found: {log_path}")
    except Exception as e:
        print(f"Error processing {tool_name}: {e}")

from pyecharts.charts import Sankey
from pyecharts import options as opts

nodes = [
    {"name": "all samples"},
    {"name": "always resolved"},
    {"name": "sometimes resolved"},
    {"name": "never resolved"},
    {"name": "grep"},
    {"name": "term_sparse"},
    {"name": "inverted_index"},
    {"name": "embedding"}
]
links = []

# 统计always resolved, sometimes resolved, never resolved   
always_resolved = []
sometimes_resolved = []
never_resolved = []
resolve_threshold = 0.4

for question, results in question_results.items():
    resolve_cnt = 0
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            resolve_cnt += 1
    if resolve_cnt == len(results):
        always_resolved.append(question)
    elif resolve_cnt > 0:
        sometimes_resolved.append(question)
    else:
        never_resolved.append(question)

links.append({"source": "all samples", "target": "always resolved", "value": len(always_resolved)})
links.append({"source": "all samples", "target": "sometimes resolved", "value": len(sometimes_resolved)})
links.append({"source": "all samples", "target": "never resolved", "value": len(never_resolved)})

# 统计always resolved, sometimes resolved, never resolved for each tool
tool_cnt = defaultdict(int)
for always_question in always_resolved:
    for tool_result in question_results[always_question]:
        tool_cnt[tool_result["tool"]] += 1
for tool, cnt in tool_cnt.items():
    links.append({"source": "always resolved", "target": tool, "value": cnt})

# 统计sometimes resolved for each tool
tool_cnt = defaultdict(int)
for question in sometimes_resolved:
    for tool_result in question_results[question]:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "sometimes resolved", "target": tool, "value": cnt})
# 统计never resolved for each tool
tool_cnt = defaultdict(int)
for question in never_resolved:
    for tool_result in question_results[question]:
        tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "never resolved", "target": tool, "value": cnt})

# 生成sankey图
pic = (
    Sankey()
    .add('', #图例名称
         nodes,    #传入节点数据
         links,   #传入边和流量数据
         #设置透明度、弯曲度、颜色
         linestyle_opt=opts.LineStyleOpts(opacity = 0.3, curve = 0.5, color = "source"),
         #标签显示位置
         label_opts=opts.LabelOpts(position="right"),
         #节点之前的距离
         node_gap = 30,
    )
    .set_global_opts(title_opts=opts.TitleOpts(title = 'Codebase Tool Effectiveness Analysis'))
)
pic.render("sankey.html")


tool_resolved_cnt = {tool: 0 for tool in }