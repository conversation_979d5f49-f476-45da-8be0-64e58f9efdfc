import re
import json
from collections import defaultdict

# 调用1次
# logs_path = {
#     "grep": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-01-20-38-21.log",
#     "term_sparse": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-34-59.log",
#     "inverted_index": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-39-45.log",
#     "embedding": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-15-25-43.log"
# }

# 调用2次
logs_path = {
    "grep": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-17-33.log",
    "term_sparse": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-39-27.log",
    "inverted_index": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-46-58.log",
    "embedding": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-34-09.log",
    "any": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-18-16-37.log"
}

# 存储所有工具的结果
all_results = {}  # key为trace_id，value为{"question": question, "recall@5": recall@5, "tool": tool}
question_results = defaultdict(list)  # key为question，value为[{"tool": tool, "recall@5": recall@5}]

for tool_name, log_path in logs_path.items():
    print(f"Processing {tool_name}: {log_path}")
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 找到所有的trace_id
        trace_pattern = r'Trace: ([a-f0-9]{32})'
        trace_ids = re.findall(trace_pattern, content)
        print(f"Found {len(set(trace_ids))} unique trace IDs in {tool_name}")
        
        # 2. 找到所有trace_id对应的question
        question_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, question: ([^\n]+)'
        question_matches = re.findall(question_pattern, content)
        trace_questions = {trace_id: question.strip() for trace_id, question in question_matches}
        print(f"Found {len(trace_questions)} trace-question pairs in {tool_name}")
        
        # 3. 找到所有trace_id对应的metrics
        # 匹配metrics块，包括多行JSON
        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, metrics: (\{[^}]*(?:\n[^}]*)*\})'
        metrics_matches = re.findall(metrics_pattern, content, re.MULTILINE | re.DOTALL)
        
        # 4. 解析metrics并提取Recall@5
        for trace_id, metrics_str in metrics_matches:
            try:
                # 清理metrics字符串，移除可能的换行和额外空格
                cleaned_metrics = re.sub(r'\n\s*', ' ', metrics_str.strip())
                metrics_json = json.loads(cleaned_metrics)
                
                recall_5 = metrics_json.get('Recall@5', None)
                if recall_5 is not None and trace_id in trace_questions:
                    # 5. 记录trace_id对应的信息
                    all_results[trace_id] = {
                        "question": trace_questions[trace_id],
                        "recall@5": recall_5,
                        "tool": tool_name
                    }
                    
                    # 6. 按question分组
                    question_results[trace_questions[trace_id]].append({
                        "tool": tool_name,
                        "recall@5": recall_5
                    })
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse metrics for trace {trace_id}: {e}")
                print(f"Metrics string: {metrics_str[:100]}...")
                continue
        
        print(f"Successfully processed {len([t for t in all_results.values() if t['tool'] == tool_name])} traces for {tool_name}")
        
    except FileNotFoundError:
        print(f"File not found: {log_path}")
    except Exception as e:
        print(f"Error processing {tool_name}: {e}")

from pyecharts.charts import Sankey
from pyecharts import options as opts

nodes = [
    {"name": "all samples"},
    {"name": "always resolved"},
    {"name": "sometimes resolved"},
    {"name": "never resolved"},
    {"name": "grep"},
    {"name": "term_sparse"},
    {"name": "inverted_index"},
    {"name": "embedding"}
]
links = []

# 统计always resolved, sometimes resolved, never resolved   
always_resolved = []
sometimes_resolved = []
never_resolved = []
resolve_threshold = 0.6

for question, results in question_results.items():
    resolve_cnt = 0
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            resolve_cnt += 1
    if resolve_cnt == len(results):
        always_resolved.append(question)
    elif resolve_cnt > 0:
        sometimes_resolved.append(question)
    else:
        never_resolved.append(question)

links.append({"source": "all samples", "target": "always resolved", "value": len(always_resolved)})
links.append({"source": "all samples", "target": "sometimes resolved", "value": len(sometimes_resolved)})
links.append({"source": "all samples", "target": "never resolved", "value": len(never_resolved)})

# 统计always resolved, sometimes resolved, never resolved for each tool
tool_cnt = defaultdict(int)
for always_question in always_resolved:
    for tool_result in question_results[always_question]:
        tool_cnt[tool_result["tool"]] += 1
for tool, cnt in tool_cnt.items():
    links.append({"source": "always resolved", "target": tool, "value": cnt})

# 统计sometimes resolved for each tool
tool_cnt = defaultdict(int)
for question in sometimes_resolved:
    for tool_result in question_results[question]:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "sometimes resolved", "target": tool, "value": cnt})
# 统计never resolved for each tool
tool_cnt = defaultdict(int)
for question in never_resolved:
    for tool_result in question_results[question]:
        tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "never resolved", "target": tool, "value": cnt})

# 生成sankey图
pic = (
    Sankey()
    .add('', #图例名称
         nodes,    #传入节点数据
         links,   #传入边和流量数据
         #设置透明度、弯曲度、颜色
         linestyle_opt=opts.LineStyleOpts(opacity = 0.3, curve = 0.5, color = "source"),
         #标签显示位置
         label_opts=opts.LabelOpts(position="right"),
         #节点之前的距离
         node_gap = 30,
    )
    .set_global_opts(title_opts=opts.TitleOpts(title = 'Codebase Tool Effectiveness Analysis'))
)
pic.render("sankey.html")


import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 设置解决问题的阈值
resolve_threshold = 0.6  # 使用与前面一致的阈值

# 获取每个工具解决的问题集合
tool_resolved_questions = {tool: set() for tool in logs_path}
for question, results in question_results.items():
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_resolved_questions[tool_result["tool"]].add(question)

default_colors = [
    # r, g, b, a
    [92, 192, 98, 0.5],
    [90, 155, 212, 0.5],
    [246, 236, 86, 0.6],
    [241, 90, 96, 0.4],
    [255, 117, 0, 0.3],
    [82, 82, 190, 0.2],
]
default_colors = [
    [i[0] / 255.0, i[1] / 255.0, i[2] / 255.0, i[3]]
    for i in default_colors
]

def draw_text(fig, ax, x, y, text, color=[0, 0, 0, 1], fontsize=14, ha="center", va="center"):
    ax.text(
        x, y, text,
        horizontalalignment=ha,
        verticalalignment=va,
        fontsize=fontsize,
        color="black")
    
def draw_ellipse(fig, ax, x, y, w, h, a, fillcolor):
    e = patches.Ellipse(
        xy=(x, y),
        width=w,
        height=h,
        angle=a,
        color=fillcolor)
    ax.add_patch(e)

def venn4(labels, names=['A', 'B', 'C', 'D'], **options):
    """
    plots a 4-set Venn diagram

    @type labels: dict[str, str]
    @type names: list[str]
    @rtype: (Figure, AxesSubplot)

    input
      labels: a label dict where keys are identified via binary codes ('0001', '0010', '0100', ...),
              hence a valid set could look like: {'0001': 'text 1', '0010': 'text 2', '0100': 'text 3', ...}.
              unmentioned codes are considered as ''.
      names:  group names
      more:   colors, figsize, dpi, fontsize

    return
      pyplot Figure and AxesSubplot object
    """
    colors = options.get('colors', [default_colors[i] for i in range(4)])
    figsize = options.get('figsize', (12, 12))
    dpi = options.get('dpi', 96)
    fontsize = options.get('fontsize', 14)

    fig = plt.figure(0, figsize=figsize, dpi=dpi)
    ax = fig.add_subplot(111, aspect='equal')
    ax.set_axis_off()
    ax.set_ylim(bottom=0.0, top=1.0)
    ax.set_xlim(left=0.0, right=1.0)

    # body
    draw_ellipse(fig, ax, 0.350, 0.400, 0.72, 0.45, 140.0, colors[0])
    draw_ellipse(fig, ax, 0.450, 0.500, 0.72, 0.45, 140.0, colors[1])
    draw_ellipse(fig, ax, 0.544, 0.500, 0.72, 0.45, 40.0, colors[2])
    draw_ellipse(fig, ax, 0.644, 0.400, 0.72, 0.45, 40.0, colors[3])
    draw_text(fig, ax, 0.85, 0.42, labels.get('0001', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.68, 0.72, labels.get('0010', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.77, 0.59, labels.get('0011', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.32, 0.72, labels.get('0100', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.71, 0.30, labels.get('0101', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.50, 0.66, labels.get('0110', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.65, 0.50, labels.get('0111', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.14, 0.42, labels.get('1000', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.50, 0.17, labels.get('1001', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.29, 0.30, labels.get('1010', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.39, 0.24, labels.get('1011', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.23, 0.59, labels.get('1100', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.61, 0.24, labels.get('1101', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.35, 0.50, labels.get('1110', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.50, 0.38, labels.get('1111', ''), fontsize=fontsize)

    # legend
    draw_text(fig, ax, 0.13, 0.18, names[0], colors[0], fontsize=fontsize, ha="right")
    draw_text(fig, ax, 0.18, 0.83, names[1], colors[1], fontsize=fontsize, ha="right", va="bottom")
    draw_text(fig, ax, 0.82, 0.83, names[2], colors[2], fontsize=fontsize, ha="left", va="bottom")
    draw_text(fig, ax, 0.87, 0.18, names[3], colors[3], fontsize=fontsize, ha="left", va="top")
    leg = ax.legend(names, loc='center left', bbox_to_anchor=(1.0, 0.5), fancybox=True)
    leg.get_frame().set_alpha(0.5)

    return fig, ax

def venn5(labels, names=['A', 'B', 'C', 'D', 'E'], **options):
    """
    plots a 5-set Venn diagram

    @type labels: dict[str, str]
    @type names: list[str]
    @rtype: (Figure, AxesSubplot)

    input
      labels: a label dict where keys are identified via binary codes ('00001', '00010', '00100', ...),
              hence a valid set could look like: {'00001': 'text 1', '00010': 'text 2', '00100': 'text 3', ...}.
              unmentioned codes are considered as ''.
      names:  group names
      more:   colors, figsize, dpi, fontsize

    return
      pyplot Figure and AxesSubplot object
    """
    colors = options.get('colors', [default_colors[i] for i in range(5)])
    figsize = options.get('figsize', (13, 13))
    dpi = options.get('dpi', 96)
    fontsize = options.get('fontsize', 14)

    fig = plt.figure(0, figsize=figsize, dpi=dpi)
    ax = fig.add_subplot(111, aspect='equal')
    ax.set_axis_off()
    ax.set_ylim(bottom=0.0, top=1.0)
    ax.set_xlim(left=0.0, right=1.0)

    # body
    draw_ellipse(fig, ax, 0.428, 0.449, 0.87, 0.50, 155.0, colors[0])
    draw_ellipse(fig, ax, 0.469, 0.543, 0.87, 0.50, 82.0, colors[1])
    draw_ellipse(fig, ax, 0.558, 0.523, 0.87, 0.50, 10.0, colors[2])
    draw_ellipse(fig, ax, 0.578, 0.432, 0.87, 0.50, 118.0, colors[3])
    draw_ellipse(fig, ax, 0.489, 0.383, 0.87, 0.50, 46.0, colors[4])
    draw_text(fig, ax, 0.27, 0.11, labels.get('00001', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.72, 0.11, labels.get('00010', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.55, 0.13, labels.get('00011', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.91, 0.58, labels.get('00100', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.78, 0.64, labels.get('00101', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.84, 0.41, labels.get('00110', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.76, 0.55, labels.get('00111', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.51, 0.90, labels.get('01000', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.39, 0.15, labels.get('01001', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.42, 0.78, labels.get('01010', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.50, 0.15, labels.get('01011', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.67, 0.76, labels.get('01100', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.70, 0.71, labels.get('01101', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.51, 0.74, labels.get('01110', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.64, 0.67, labels.get('01111', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.10, 0.61, labels.get('10000', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.20, 0.31, labels.get('10001', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.76, 0.25, labels.get('10010', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.65, 0.23, labels.get('10011', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.18, 0.50, labels.get('10100', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.21, 0.37, labels.get('10101', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.81, 0.37, labels.get('10110', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.74, 0.40, labels.get('10111', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.27, 0.70, labels.get('11000', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.34, 0.25, labels.get('11001', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.33, 0.72, labels.get('11010', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.51, 0.22, labels.get('11011', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.25, 0.58, labels.get('11100', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.28, 0.39, labels.get('11101', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.36, 0.66, labels.get('11110', ''), fontsize=fontsize)
    draw_text(fig, ax, 0.51, 0.47, labels.get('11111', ''), fontsize=fontsize)

    # legend
    draw_text(fig, ax, 0.02, 0.72, names[0], colors[0], fontsize=fontsize, ha="right")
    draw_text(fig, ax, 0.72, 0.94, names[1], colors[1], fontsize=fontsize, va="bottom")
    draw_text(fig, ax, 0.97, 0.74, names[2], colors[2], fontsize=fontsize, ha="left")
    draw_text(fig, ax, 0.88, 0.05, names[3], colors[3], fontsize=fontsize, ha="left")
    draw_text(fig, ax, 0.12, 0.05, names[4], colors[4], fontsize=fontsize, ha="right")
    leg = ax.legend(names, loc='center left', bbox_to_anchor=(1.0, 0.5), fancybox=True)
    leg.get_frame().set_alpha(0.5)

    return fig, ax


import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 设置解决问题的阈值
resolve_threshold = 0.6

# 获取每个工具解决的问题集合
tool_resolved_questions = {tool: set() for tool in logs_path}
for question, results in question_results.items():
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_resolved_questions[tool_result["tool"]].add(question)

# 打印每个工具解决的问题数量
print("每个工具解决的问题数量:")
for tool, questions in tool_resolved_questions.items():
    print(f"{tool}: {len(questions)} 个问题")

# 准备五个工具的集合
tools = ['grep', 'term_sparse', 'inverted_index', 'embedding', 'any']
tool_sets = [tool_resolved_questions[tool] for tool in tools]

# 计算所有可能的交集组合并生成标签字典
labels = {}

# 生成所有32种可能的组合 (2^5 = 32)
for i in range(32):
    # 将数字转换为4位二进制字符串
    binary = format(i, '05b')
    
    # 根据二进制位确定当前组合包含哪些集合
    current_intersection = None
    for j, bit in enumerate(binary):
        if bit == '1':
            if current_intersection is None:
                current_intersection = tool_sets[j].copy()
            else:
                current_intersection = current_intersection.intersection(tool_sets[j])
    
    # 如果没有选中任何集合，跳过
    if current_intersection is None:
        continue
    
    # 计算这个交集中排除其他集合的元素数量
    exclusive_intersection = current_intersection.copy()
    for j, bit in enumerate(binary):
        if bit == '0':
            exclusive_intersection = exclusive_intersection - tool_sets[j]
    
    # 只有当排除交集不为空时才添加标签
    if len(exclusive_intersection) > 0:
        labels[binary] = str(len(exclusive_intersection))

# 使用自定义的venn4函数绘制维恩图
plt.figure(figsize=(12, 10))

# 调用你的venn4函数
fig, ax = venn5(labels, names=tools, figsize=(12, 10), fontsize=12)

# 设置标题
plt.suptitle(f'Four Tools Problem Solving Overlap (Recall@5 >= {resolve_threshold})', 
             fontsize=16, fontweight='bold', y=0.95)

# 添加统计信息
total_problems = len(set().union(*tool_sets))
stats_text = f"""Statistics:
Total unique problems: {total_problems}
grep: {len(tool_sets[0])} problems
term_sparse: {len(tool_sets[1])} problems  
inverted_index: {len(tool_sets[2])} problems
embedding: {len(tool_sets[3])} problems"""

plt.figtext(0.02, 0.02, stats_text, fontsize=10, 
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

plt.tight_layout()
plt.show()


# 打印详细的重合分析
print("\n=== 工具间重合分析 ===")

# 计算两两交集
print("\n两两交集:")
for i in range(len(tools)):
    for j in range(i+1, len(tools)):
        intersection = tool_sets[i].intersection(tool_sets[j])
        union = tool_sets[i].union(tool_sets[j])
        jaccard = len(intersection) / len(union) if len(union) > 0 else 0
        print(f"{tools[i]} ∩ {tools[j]}: {len(intersection)} 个问题 (Jaccard相似度: {jaccard:.3f})")

# 计算所有工具的交集
all_intersection = set.intersection(*tool_sets)
print(f"\n所有工具共同解决的问题: {len(all_intersection)} 个")

# 计算每个工具独有的问题
print("\n每个工具独有的问题:")
for i, tool in enumerate(tools):
    unique = tool_sets[i]
    for j, other_set in enumerate(tool_sets):
        if i != j:
            unique = unique - other_set
    print(f"{tool} 独有: {len(unique)} 个问题")

# 如果所有工具都能解决的问题不为空，显示一些例子
if len(all_intersection) > 0:
    print(f"\n所有工具都能解决的问题示例 (前5个):")
    for i, question in enumerate(list(all_intersection)[:5]):
        print(f"{i+1}. {question[:100]}...")

# 打印生成的标签字典以供调试
print("\n=== 维恩图标签字典 ===")
for binary, count in sorted(labels.items()):
    tool_names = [tools[i] for i, bit in enumerate(binary) if bit == '1']
    print(f"{binary} ({' ∩ '.join(tool_names)}): {count} 个问题")


