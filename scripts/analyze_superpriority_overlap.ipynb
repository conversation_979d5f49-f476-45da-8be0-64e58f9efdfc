import re
import json
from collections import defaultdict

logs_path = {
    "grep": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-01-20-38-21.log",
    "term_sparse": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-34-59.log",
    "inverted_index": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-39-45.log",
    "embedding": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-08-31-14-04-41.log"
}

# 存储所有工具的结果
all_results = {}  # key为trace_id，value为{"question": question, "recall@5": recall@5, "tool": tool}
question_results = defaultdict(list)  # key为question，value为[{"tool": tool, "recall@5": recall@5}]

for tool_name, log_path in logs_path.items():
    print(f"Processing {tool_name}: {log_path}")
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 找到所有的trace_id
        trace_pattern = r'Trace: ([a-f0-9]{32})'
        trace_ids = re.findall(trace_pattern, content)
        print(f"Found {len(set(trace_ids))} unique trace IDs in {tool_name}")
        
        # 2. 找到所有trace_id对应的question
        question_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, question: ([^\n]+)'
        question_matches = re.findall(question_pattern, content)
        trace_questions = {trace_id: question.strip() for trace_id, question in question_matches}
        print(f"Found {len(trace_questions)} trace-question pairs in {tool_name}")
        
        # 3. 找到所有trace_id对应的metrics
        # 匹配metrics块，包括多行JSON
        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, metrics: (\{[^}]*(?:\n[^}]*)*\})'
        metrics_matches = re.findall(metrics_pattern, content, re.MULTILINE | re.DOTALL)
        
        # 4. 解析metrics并提取Recall@5
        for trace_id, metrics_str in metrics_matches:
            try:
                # 清理metrics字符串，移除可能的换行和额外空格
                cleaned_metrics = re.sub(r'\n\s*', ' ', metrics_str.strip())
                metrics_json = json.loads(cleaned_metrics)
                
                recall_5 = metrics_json.get('Recall@5', None)
                if recall_5 is not None and trace_id in trace_questions:
                    # 5. 记录trace_id对应的信息
                    all_results[trace_id] = {
                        "question": trace_questions[trace_id],
                        "recall@5": recall_5,
                        "tool": tool_name
                    }
                    
                    # 6. 按question分组
                    question_results[trace_questions[trace_id]].append({
                        "tool": tool_name,
                        "recall@5": recall_5
                    })
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse metrics for trace {trace_id}: {e}")
                print(f"Metrics string: {metrics_str[:100]}...")
                continue
        
        print(f"Successfully processed {len([t for t in all_results.values() if t['tool'] == tool_name])} traces for {tool_name}")
        
    except FileNotFoundError:
        print(f"File not found: {log_path}")
    except Exception as e:
        print(f"Error processing {tool_name}: {e}")

from pyecharts.charts import Sankey
from pyecharts import options as opts

nodes = [
    {"name": "all samples"},
    {"name": "always resolved"},
    {"name": "sometimes resolved"},
    {"name": "never resolved"},
    {"name": "grep"},
    {"name": "term_sparse"},
    {"name": "inverted_index"},
    {"name": "embedding"}
]
links = []

# 统计always resolved, sometimes resolved, never resolved   
always_resolved = []
sometimes_resolved = []
never_resolved = []
resolve_threshold = 0.4

for question, results in question_results.items():
    resolve_cnt = 0
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            resolve_cnt += 1
    if resolve_cnt == len(results):
        always_resolved.append(question)
    elif resolve_cnt > 0:
        sometimes_resolved.append(question)
    else:
        never_resolved.append(question)

links.append({"source": "all samples", "target": "always resolved", "value": len(always_resolved)})
links.append({"source": "all samples", "target": "sometimes resolved", "value": len(sometimes_resolved)})
links.append({"source": "all samples", "target": "never resolved", "value": len(never_resolved)})

# 统计always resolved, sometimes resolved, never resolved for each tool
tool_cnt = defaultdict(int)
for always_question in always_resolved:
    for tool_result in question_results[always_question]:
        tool_cnt[tool_result["tool"]] += 1
for tool, cnt in tool_cnt.items():
    links.append({"source": "always resolved", "target": tool, "value": cnt})

# 统计sometimes resolved for each tool
tool_cnt = defaultdict(int)
for question in sometimes_resolved:
    for tool_result in question_results[question]:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "sometimes resolved", "target": tool, "value": cnt})
# 统计never resolved for each tool
tool_cnt = defaultdict(int)
for question in never_resolved:
    for tool_result in question_results[question]:
        tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "never resolved", "target": tool, "value": cnt})

# 生成sankey图
pic = (
    Sankey()
    .add('', #图例名称
         nodes,    #传入节点数据
         links,   #传入边和流量数据
         #设置透明度、弯曲度、颜色
         linestyle_opt=opts.LineStyleOpts(opacity = 0.3, curve = 0.5, color = "source"),
         #标签显示位置
         label_opts=opts.LabelOpts(position="right"),
         #节点之前的距离
         node_gap = 30,
    )
    .set_global_opts(title_opts=opts.TitleOpts(title = 'Codebase Tool Effectiveness Analysis'))
)
pic.render("sankey.html")


import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib_venn import venn2, venn3
import numpy as np

# 设置解决问题的阈值
resolve_threshold = 0.5  # 可以根据需要调整这个阈值

tool_resolved_questions = {tool: set() for tool in logs_path}
for question, results in question_results.items():
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_resolved_questions[tool_result["tool"]].add(question)

# 打印每个工具解决的问题数量
print("每个工具解决的问题数量:")
for tool, questions in tool_resolved_questions.items():
    print(f"{tool}: {len(questions)} 个问题")

# 绘制维恩图
plt.figure(figsize=(15, 10))

# 获取工具列表
tools = list(tool_resolved_questions.keys())
tool_sets = [tool_resolved_questions[tool] for tool in tools]

if len(tools) == 2:
    # 两个工具的维恩图
    plt.subplot(1, 1, 1)
    venn2(tool_sets, set_labels=tools)
    plt.title("两个工具解决问题的重合情况")
    
elif len(tools) == 3:
    # 三个工具的维恩图
    plt.subplot(1, 1, 1)
    venn3(tool_sets, set_labels=tools)
    plt.title("三个工具解决问题的重合情况")
    
elif len(tools) == 4:
    # 四个工具的维恩图（使用两两比较的方式）
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle("工具间两两重合情况分析", fontsize=16)
    
    comparisons = [
        (0, 1), (0, 2), (0, 3),
        (1, 2), (1, 3), (2, 3)
    ]
    
    for idx, (i, j) in enumerate(comparisons):
        ax = axes[idx // 3, idx % 3]
        venn2([tool_sets[i], tool_sets[j]], 
              set_labels=[tools[i], tools[j]], ax=ax)
        ax.set_title(f"{tools[i]} vs {tools[j]}")

plt.tight_layout()
plt.show()

# 详细分析重合情况
print("\n详细重合分析:")

# 计算所有工具的交集和并集
all_questions = set()
for questions in tool_resolved_questions.values():
    all_questions.update(questions)

print(f"总共有 {len(all_questions)} 个不同的问题被至少一个工具解决")

# 分析每个问题被多少个工具解决
question_tool_count = {}
for question in all_questions:
    count = 0
    solving_tools = []
    for tool, questions in tool_resolved_questions.items():
        if question in questions:
            count += 1
            solving_tools.append(tool)
    question_tool_count[question] = {
        'count': count,
        'tools': solving_tools
    }

# 统计被不同数量工具解决的问题
tool_count_stats = {}
for question, info in question_tool_count.items():
    count = info['count']
    if count not in tool_count_stats:
        tool_count_stats[count] = []
    tool_count_stats[count].append(question)

print("\n问题解决情况统计:")
for count in sorted(tool_count_stats.keys(), reverse=True):
    questions = tool_count_stats[count]
    print(f"被 {count} 个工具解决的问题: {len(questions)} 个")
    if count == len(tools):
        print("  (所有工具都能解决的问题)")
    elif count == 1:
        print("  (只有一个工具能解决的问题)")

# 找出所有工具都能解决的问题
if len(tools) in tool_count_stats:
    common_questions = tool_count_stats[len(tools)]
    print(f"\n所有工具都能解决的问题 ({len(common_questions)} 个):")
    for i, question in enumerate(common_questions[:5]):  # 只显示前5个
        print(f"  {i+1}. {question[:100]}...")
    if len(common_questions) > 5:
        print(f"  ... 还有 {len(common_questions) - 5} 个问题")

# 找出只有一个工具能解决的问题
if 1 in tool_count_stats:
    unique_questions = tool_count_stats[1]
    print(f"\n只有一个工具能解决的问题 ({len(unique_questions)} 个):")
    tool_unique_count = {}
    for question in unique_questions:
        tool = question_tool_count[question]['tools'][0]
        if tool not in tool_unique_count:
            tool_unique_count[tool] = []
        tool_unique_count[tool].append(question)
    
    for tool, questions in tool_unique_count.items():
        print(f"  {tool} 独有: {len(questions)} 个问题")

# 绘制统计柱状图
plt.figure(figsize=(16, 12))

# 子图1: 每个工具解决的问题数量
plt.subplot(2, 2, 1)
tools_list = list(tool_resolved_questions.keys())
counts = [len(tool_resolved_questions[tool]) for tool in tools_list]
plt.bar(tools_list, counts, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
plt.title("每个工具解决的问题数量")
plt.xticks(rotation=45)
plt.ylabel("问题数量")
for i, count in enumerate(counts):
    plt.text(i, count + 0.5, str(count), ha='center')

# 子图2: 被不同数量工具解决的问题分布
plt.subplot(2, 2, 2)
tool_counts = sorted(tool_count_stats.keys())
question_counts = [len(tool_count_stats[count]) for count in tool_counts]
colors = ['red', 'orange', 'yellow', 'green']
plt.bar(tool_counts, question_counts, color=colors[:len(tool_counts)])
plt.title("问题被工具解决的分布")
plt.xlabel("解决该问题的工具数量")
plt.ylabel("问题数量")
for i, count in enumerate(question_counts):
    plt.text(tool_counts[i], count + 0.5, str(count), ha='center')

# 子图3: 工具独有问题数量
plt.subplot(2, 2, 3)
if 1 in tool_count_stats:
    unique_counts = []
    for tool in tools_list:
        count = len([q for q in tool_count_stats[1] 
                    if question_tool_count[q]['tools'][0] == tool])
        unique_counts.append(count)
    plt.bar(tools_list, unique_counts, color=['lightblue', 'lightpink', 'lightyellow', 'lightgray'])
    plt.title("每个工具独有的问题数量")
    plt.xticks(rotation=45)
    plt.ylabel("独有问题数量")
    for i, count in enumerate(unique_counts):
        plt.text(i, count + 0.1, str(count), ha='center')

# 子图4: 重合度矩阵热图
plt.subplot(2, 2, 4)
n_tools = len(tools_list)
overlap_matrix = np.zeros((n_tools, n_tools))

for i in range(n_tools):
    for j in range(n_tools):
        if i == j:
            overlap_matrix[i][j] = len(tool_sets[i])
        else:
            intersection = len(tool_sets[i].intersection(tool_sets[j]))
            overlap_matrix[i][j] = intersection

im = plt.imshow(overlap_matrix, cmap='Blues')
plt.colorbar(im)
plt.xticks(range(n_tools), tools_list, rotation=45)
plt.yticks(range(n_tools), tools_list)
plt.title("工具间问题重合数量矩阵")

# 在矩阵中添加数值标注
for i in range(n_tools):
    for j in range(n_tools):
        plt.text(j, i, int(overlap_matrix[i][j]), 
                ha="center", va="center", color="red", fontweight='bold')

plt.tight_layout()
plt.show()

# 计算并显示重合度百分比
print("\n工具间重合度分析:")
for i, tool1 in enumerate(tools_list):
    for j, tool2 in enumerate(tools_list):
        if i < j:  # 避免重复计算
            intersection = len(tool_sets[i].intersection(tool_sets[j]))
            union = len(tool_sets[i].union(tool_sets[j]))
            jaccard = intersection / union if union > 0 else 0
            overlap_percent1 = intersection / len(tool_sets[i]) * 100 if len(tool_sets[i]) > 0 else 0
            overlap_percent2 = intersection / len(tool_sets[j]) * 100 if len(tool_sets[j]) > 0 else 0
            
            print(f"{tool1} vs {tool2}:")
            print(f"  共同解决: {intersection} 个问题")
            print(f"  Jaccard相似度: {jaccard:.3f}")
            print(f"  {tool1}的重合度: {overlap_percent1:.1f}%")
            print(f"  {tool2}的重合度: {overlap_percent2:.1f}%")
            print()


