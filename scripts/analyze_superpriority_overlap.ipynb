logs_path = [
    {"grep": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-01-20-38-21.log"},
    {"term_sparse": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-34-59.log"},
    {"inverted_index": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-39-45.log"},
    {"embedding": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-08-31-14-04-41.log"}
]

from pyecharts.charts import Sankey
from pyecharts import options as opts

nodes = [
    {"name": "all samples"},
    {"name": "always resolved"},
    {"name": "sometimes resolved"},
    {"name": "never resolved"},
    {"name": "grep"},
    {"name": "term_sparse"},
    {"name": "inverted_index"},
    {"name": "embedding"}
]

import os 

# 示例log文件
for tool_name, log_path in logs_path.items():
    # 1. 找到所有的trace_id, 从文件中匹配的内容如：Trace: 6e349a0126b842ac8dd92ce5f2cd69ea
    # 2. 找到所有trace_id对应的question，从文件中匹配的内容如：Trace: 6e349a0126b842ac8dd92ce5f2cd69ea, project: PyMySQL, question: Analyze SQL injection protection mechanisms in PyMySQL and check if parameterized query implementation is secure
    # 3. 找到所有trace_id对应的指标，匹配的内容如：2025-09-01 00:02:47 - coderetrievalbenchmarks - INFO - Trace: 6e349a0126b842ac8dd92ce5f2cd69ea, project: PyMySQL, metrics: {
    # "P@5": 0.4,
    # "Recall@5": 0.6666666666666666,
    # "NDCG@5": 0.4367467095119258,
    # "MAP@5": 0.27777777777777773,
    # "P@10": 0.2,
    # "Recall@10": 0.6666666666666666,
    # "NDCG@10": 0.4367467095119258,
    # "MAP@10": 0.27777777777777773,
    # "P@30": 0.2,
    # "Recall@30": 0.6666666666666666,
    # "NDCG@30": 0.4367467095119258,
    # "MAP@30": 0.27777777777777773
    # }
    # 4. 将找到的metrics解析为json，记录下每个trace_id对应的Recall@5的指标
    # 5. 将找到的question和recall@5的指标，记录到一个dict中，key为trace_id，value为{"question": question, "recall@5": recall@5}
    # 6. 遍历所有的log文件，最终得到一个dict，key为question，value为[{"tool": tool, "recall@5": recall@5"}]

