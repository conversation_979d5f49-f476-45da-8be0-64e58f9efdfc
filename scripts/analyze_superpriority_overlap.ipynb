import re
import json
from collections import defaultdict

logs_path = {
    "grep": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-01-20-38-21.log",
    "term_sparse": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-34-59.log",
    "inverted_index": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-39-45.log",
    "embedding": "/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-08-31-14-04-41.log"
}

# 存储所有工具的结果
all_results = {}  # key为trace_id，value为{"question": question, "recall@5": recall@5, "tool": tool}
question_results = defaultdict(list)  # key为question，value为[{"tool": tool, "recall@5": recall@5}]

for tool_name, log_path in logs_path.items():
    print(f"Processing {tool_name}: {log_path}")
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 找到所有的trace_id
        trace_pattern = r'Trace: ([a-f0-9]{32})'
        trace_ids = re.findall(trace_pattern, content)
        print(f"Found {len(set(trace_ids))} unique trace IDs in {tool_name}")
        
        # 2. 找到所有trace_id对应的question
        question_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, question: ([^\n]+)'
        question_matches = re.findall(question_pattern, content)
        trace_questions = {trace_id: question.strip() for trace_id, question in question_matches}
        print(f"Found {len(trace_questions)} trace-question pairs in {tool_name}")
        
        # 3. 找到所有trace_id对应的metrics
        # 匹配metrics块，包括多行JSON
        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, metrics: (\{[^}]*(?:\n[^}]*)*\})'
        metrics_matches = re.findall(metrics_pattern, content, re.MULTILINE | re.DOTALL)
        
        # 4. 解析metrics并提取Recall@5
        for trace_id, metrics_str in metrics_matches:
            try:
                # 清理metrics字符串，移除可能的换行和额外空格
                cleaned_metrics = re.sub(r'\n\s*', ' ', metrics_str.strip())
                metrics_json = json.loads(cleaned_metrics)
                
                recall_5 = metrics_json.get('Recall@5', None)
                if recall_5 is not None and trace_id in trace_questions:
                    # 5. 记录trace_id对应的信息
                    all_results[trace_id] = {
                        "question": trace_questions[trace_id],
                        "recall@5": recall_5,
                        "tool": tool_name
                    }
                    
                    # 6. 按question分组
                    question_results[trace_questions[trace_id]].append({
                        "tool": tool_name,
                        "recall@5": recall_5
                    })
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse metrics for trace {trace_id}: {e}")
                print(f"Metrics string: {metrics_str[:100]}...")
                continue
        
        print(f"Successfully processed {len([t for t in all_results.values() if t['tool'] == tool_name])} traces for {tool_name}")
        
    except FileNotFoundError:
        print(f"File not found: {log_path}")
    except Exception as e:
        print(f"Error processing {tool_name}: {e}")

from pyecharts.charts import Sankey
from pyecharts import options as opts

nodes = [
    {"name": "all samples"},
    {"name": "always resolved"},
    {"name": "sometimes resolved"},
    {"name": "never resolved"},
    {"name": "grep"},
    {"name": "term_sparse"},
    {"name": "inverted_index"},
    {"name": "embedding"}
]
links = []

# 统计always resolved, sometimes resolved, never resolved   
always_resolved = []
sometimes_resolved = []
never_resolved = []
resolve_threshold = 0.4

for question, results in question_results.items():
    resolve_cnt = 0
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            resolve_cnt += 1
    if resolve_cnt == len(results):
        always_resolved.append(question)
    elif resolve_cnt > 0:
        sometimes_resolved.append(question)
    else:
        never_resolved.append(question)

links.append({"source": "all samples", "target": "always resolved", "value": len(always_resolved)})
links.append({"source": "all samples", "target": "sometimes resolved", "value": len(sometimes_resolved)})
links.append({"source": "all samples", "target": "never resolved", "value": len(never_resolved)})

# 统计always resolved, sometimes resolved, never resolved for each tool
tool_cnt = defaultdict(int)
for always_question in always_resolved:
    for tool_result in question_results[always_question]:
        tool_cnt[tool_result["tool"]] += 1
for tool, cnt in tool_cnt.items():
    links.append({"source": "always resolved", "target": tool, "value": cnt})

# 统计sometimes resolved for each tool
tool_cnt = defaultdict(int)
for question in sometimes_resolved:
    for tool_result in question_results[question]:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "sometimes resolved", "target": tool, "value": cnt})
# 统计never resolved for each tool
tool_cnt = defaultdict(int)
for question in never_resolved:
    for tool_result in question_results[question]:
        tool_cnt[tool_result["tool"]] += 1

for tool, cnt in tool_cnt.items():
    links.append({"source": "never resolved", "target": tool, "value": cnt})

# 生成sankey图
pic = (
    Sankey()
    .add('', #图例名称
         nodes,    #传入节点数据
         links,   #传入边和流量数据
         #设置透明度、弯曲度、颜色
         linestyle_opt=opts.LineStyleOpts(opacity = 0.3, curve = 0.5, color = "source"),
         #标签显示位置
         label_opts=opts.LabelOpts(position="right"),
         #节点之前的距离
         node_gap = 30,
    )
    .set_global_opts(title_opts=opts.TitleOpts(title = 'Codebase Tool Effectiveness Analysis'))
)
pic.render("sankey.html")


import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib_venn import veen
import numpy as np

# 设置解决问题的阈值
resolve_threshold = 0.5  # 可以根据需要调整这个阈值

tool_resolved_questions = {tool: set() for tool in logs_path}
for question, results in question_results.items():
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_resolved_questions[tool_result["tool"]].add(question)

# 绘制维恩图，


import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 设置解决问题的阈值
resolve_threshold = 0.4  # 使用与前面一致的阈值

# 获取每个工具解决的问题集合
tool_resolved_questions = {tool: set() for tool in logs_path}
for question, results in question_results.items():
    for tool_result in results:
        if tool_result["recall@5"] >= resolve_threshold:
            tool_resolved_questions[tool_result["tool"]].add(question)

# 打印每个工具解决的问题数量
print("每个工具解决的问题数量:")
for tool, questions in tool_resolved_questions.items():
    print(f"{tool}: {len(questions)} 个问题")

# 创建四个工具的维恩图
def draw_four_way_venn():
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # 定义四个圆的位置和半径
    circles = [
        {'center': (0.3, 0.6), 'radius': 0.25, 'color': 'red', 'alpha': 0.3, 'label': 'grep'},
        {'center': (0.7, 0.6), 'radius': 0.25, 'color': 'blue', 'alpha': 0.3, 'label': 'term_sparse'},
        {'center': (0.3, 0.4), 'radius': 0.25, 'color': 'green', 'alpha': 0.3, 'label': 'inverted_index'},
        {'center': (0.7, 0.4), 'radius': 0.25, 'color': 'orange', 'alpha': 0.3, 'label': 'embedding'}
    ]
    
    # 绘制四个圆
    for circle in circles:
        circle_patch = patches.Circle(circle['center'], circle['radius'], 
                                    color=circle['color'], alpha=circle['alpha'])
        ax.add_patch(circle_patch)
        
        # 添加标签
        label_x = circle['center'][0]
        label_y = circle['center'][1] + circle['radius'] + 0.05
        ax.text(label_x, label_y, circle['label'], ha='center', va='bottom', 
               fontsize=12, fontweight='bold')
    
    # 获取工具数据
    tools = ['grep', 'term_sparse', 'inverted_index', 'embedding']
    tool_sets = [tool_resolved_questions[tool] for tool in tools]
    
    # 在每个圆心显示该工具解决的问题总数
    for i, (tool, circle) in enumerate(zip(tools, circles)):
        ax.text(circle['center'][0], circle['center'][1], str(len(tool_sets[i])), 
               ha='center', va='center', fontsize=14, fontweight='bold', color='white')
    
    # 计算两两交集并在交集区域显示数量
    intersections = {}
    for i in range(len(tools)):
        for j in range(i+1, len(tools)):
            intersection = tool_sets[i].intersection(tool_sets[j])
            intersections[f"{tools[i]}__{tools[j]}"] = len(intersection)
    
    # 在图上显示交集信息（简化版本，只显示主要交集）
    intersection_positions = [
        (0.5, 0.65, 'grep__term_sparse'),  # grep & term_sparse (上方交集)
        (0.2, 0.5, 'grep__inverted_index'),   # grep & inverted_index (左方交集)
        (0.5, 0.35, 'inverted_index__embedding'),  # inverted_index & embedding (下方交集)
        (0.8, 0.5, 'term_sparse__embedding'),   # term_sparse & embedding (右方交集)
    ]
    
    for pos_x, pos_y, key in intersection_positions:
        if key in intersections:
            ax.text(pos_x, pos_y, str(intersections[key]), 
                   ha='center', va='center', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    # 设置图形属性
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_aspect('equal')
    ax.axis('off')
    ax.set_title('Four Tools Problem Solving Overlap Venn Diagram\\n(Numbers in circles: total problems solved | Numbers in intersections: shared problems)', 
                fontsize=14, fontweight='bold', pad=20)
    
    # 添加图例说明
    total_problems = len(set().union(*tool_sets))
    legend_text = f"""Threshold: Recall@5 >= {resolve_threshold}
Total unique problems: {total_problems}
Circle numbers: Total problems solved by each tool
Intersection numbers: Problems solved by both tools"""
    
    ax.text(0.02, 0.02, legend_text, transform=ax.transAxes, 
           fontsize=10, verticalalignment='bottom',
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

# 绘制维恩图
draw_four_way_venn()

# 打印详细的重合分析
print("\n=== 工具间重合分析 ===")
tools = ['grep', 'term_sparse', 'inverted_index', 'embedding']
tool_sets = [tool_resolved_questions[tool] for tool in tools]

# 计算所有可能的交集
print("\n两两交集:")
for i in range(len(tools)):
    for j in range(i+1, len(tools)):
        intersection = tool_sets[i].intersection(tool_sets[j])
        union = tool_sets[i].union(tool_sets[j])
        jaccard = len(intersection) / len(union) if len(union) > 0 else 0
        print(f"{tools[i]} ∩ {tools[j]}: {len(intersection)} 个问题 (Jaccard相似度: {jaccard:.3f})")

# 计算所有工具的交集
all_intersection = set.intersection(*tool_sets)
print(f"\n所有工具共同解决的问题: {len(all_intersection)} 个")

# 计算每个工具独有的问题
print("\n每个工具独有的问题:")
for i, tool in enumerate(tools):
    unique = tool_sets[i]
    for j, other_set in enumerate(tool_sets):
        if i != j:
            unique = unique - other_set
    print(f"{tool} 独有: {len(unique)} 个问题")


