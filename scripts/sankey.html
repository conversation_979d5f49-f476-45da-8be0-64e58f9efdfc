<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ef744f7c0387438ea7b7a9b23f7d99e2" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_ef744f7c0387438ea7b7a9b23f7d99e2 = echarts.init(
            document.getElementById('ef744f7c0387438ea7b7a9b23f7d99e2'), 'white', {renderer: 'canvas'});
        var option_ef744f7c0387438ea7b7a9b23f7d99e2 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "all samples"
                },
                {
                    "name": "always resolved"
                },
                {
                    "name": "sometimes resolved"
                },
                {
                    "name": "never resolved"
                },
                {
                    "name": "grep"
                },
                {
                    "name": "term_sparse"
                },
                {
                    "name": "inverted_index"
                },
                {
                    "name": "embedding"
                }
            ],
            "links": [
                {
                    "source": "all samples",
                    "target": "always resolved",
                    "value": 26
                },
                {
                    "source": "all samples",
                    "target": "sometimes resolved",
                    "value": 84
                },
                {
                    "source": "all samples",
                    "target": "never resolved",
                    "value": 39
                },
                {
                    "source": "always resolved",
                    "target": "grep",
                    "value": 26
                },
                {
                    "source": "always resolved",
                    "target": "term_sparse",
                    "value": 26
                },
                {
                    "source": "always resolved",
                    "target": "inverted_index",
                    "value": 26
                },
                {
                    "source": "always resolved",
                    "target": "embedding",
                    "value": 26
                },
                {
                    "source": "sometimes resolved",
                    "target": "grep",
                    "value": 60
                },
                {
                    "source": "sometimes resolved",
                    "target": "embedding",
                    "value": 44
                },
                {
                    "source": "sometimes resolved",
                    "target": "term_sparse",
                    "value": 43
                },
                {
                    "source": "sometimes resolved",
                    "target": "inverted_index",
                    "value": 27
                },
                {
                    "source": "never resolved",
                    "target": "grep",
                    "value": 39
                },
                {
                    "source": "never resolved",
                    "target": "term_sparse",
                    "value": 39
                },
                {
                    "source": "never resolved",
                    "target": "inverted_index",
                    "value": 39
                },
                {
                    "source": "never resolved",
                    "target": "embedding",
                    "value": 39
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 30,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "Codebase Tool Effectiveness Analysis",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_ef744f7c0387438ea7b7a9b23f7d99e2.setOption(option_ef744f7c0387438ea7b7a9b23f7d99e2);
    </script>
</body>
</html>
