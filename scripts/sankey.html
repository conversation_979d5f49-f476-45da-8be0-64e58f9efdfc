<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="fe72ea5c572d492e9a0d0dbbeb4c632b" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_fe72ea5c572d492e9a0d0dbbeb4c632b = echarts.init(
            document.getElementById('fe72ea5c572d492e9a0d0dbbeb4c632b'), 'white', {renderer: 'canvas'});
        var option_fe72ea5c572d492e9a0d0dbbeb4c632b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "all samples"
                },
                {
                    "name": "always resolved"
                },
                {
                    "name": "sometimes resolved"
                },
                {
                    "name": "never resolved"
                },
                {
                    "name": "grep"
                },
                {
                    "name": "term_sparse"
                },
                {
                    "name": "inverted_index"
                },
                {
                    "name": "embedding"
                }
            ],
            "links": [
                {
                    "source": "all samples",
                    "target": "always resolved",
                    "value": 22
                },
                {
                    "source": "all samples",
                    "target": "sometimes resolved",
                    "value": 56
                },
                {
                    "source": "all samples",
                    "target": "never resolved",
                    "value": 71
                },
                {
                    "source": "always resolved",
                    "target": "grep",
                    "value": 22
                },
                {
                    "source": "always resolved",
                    "target": "term_sparse",
                    "value": 22
                },
                {
                    "source": "always resolved",
                    "target": "inverted_index",
                    "value": 22
                },
                {
                    "source": "always resolved",
                    "target": "embedding",
                    "value": 22
                },
                {
                    "source": "sometimes resolved",
                    "target": "grep",
                    "value": 24
                },
                {
                    "source": "sometimes resolved",
                    "target": "term_sparse",
                    "value": 31
                },
                {
                    "source": "sometimes resolved",
                    "target": "embedding",
                    "value": 38
                },
                {
                    "source": "sometimes resolved",
                    "target": "inverted_index",
                    "value": 15
                },
                {
                    "source": "never resolved",
                    "target": "grep",
                    "value": 71
                },
                {
                    "source": "never resolved",
                    "target": "term_sparse",
                    "value": 71
                },
                {
                    "source": "never resolved",
                    "target": "inverted_index",
                    "value": 71
                },
                {
                    "source": "never resolved",
                    "target": "embedding",
                    "value": 71
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 30,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "Codebase Tool Effectiveness Analysis",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_fe72ea5c572d492e9a0d0dbbeb4c632b.setOption(option_fe72ea5c572d492e9a0d0dbbeb4c632b);
    </script>
</body>
</html>
